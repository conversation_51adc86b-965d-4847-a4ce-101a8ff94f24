<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-S55EPWPGH7"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-S55EPWPGH7');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Al Manar</title>
    <meta name="description" content="مجموعة مدارس المنار الاهلية - نظام إدارة الطلاب">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='login.css') }}">
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</head>
<body class="fade-in">
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle theme">
        <i class="fas fa-moon" id="theme-icon"></i>
    </button>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container"></div>

    <!-- Floating Particles Background -->
    <div class="login-particles" id="particles"></div>

    <!-- Login Container -->
    <div class="login-container slide-up">
        <h1 class="login-title">مجموعة مدارس المنار الاهلية</h1>
        <form class="login-form" onsubmit="login(event)">
            <input type="text" name="username" placeholder="اسم المستخدم" required autocomplete="username">
            <div class="password-container">
                <input type="password" id="password" name="password" placeholder="كلمة السر" required autocomplete="current-password">
                <span class="toggle-password" onclick="togglePasswordVisibility()" aria-label="Toggle password visibility">
                    <i id="password-eye" class="fas fa-eye-slash"></i>
                </span>
            </div>
            <button type="submit">
                <span>تسجيل الدخول</span>
            </button>
        </form>
    </div>

    <!-- Logged In Box -->
    <div onclick="window.location.href='/dashboard';" id="logged-in-box" class="scale-in">
        <p>logged in as <span id="logged-in-username"></span></p>
    </div>

    <script>
        // Initialize particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 10 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 6) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Theme toggle functionality
        function toggleTheme() {
            const html = document.documentElement;
            const themeIcon = document.getElementById('theme-icon');
            const currentTheme = html.getAttribute('data-theme');

            if (currentTheme === 'dark') {
                html.setAttribute('data-theme', 'light');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'light');
            } else {
                html.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Initialize theme
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            const html = document.documentElement;
            const themeIcon = document.getElementById('theme-icon');

            html.setAttribute('data-theme', savedTheme);
            themeIcon.className = savedTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
        }

        // Check if user is already logged in
        !async function(){
            await checkToken() && (
                document.getElementById("logged-in-box").style.display="block",
                document.getElementById("logged-in-username").innerHTML=localStorage.getItem("name").replace(/"/g,"")
            );
        }();

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            createParticles();
        });
    </script>
</body>
</html>