* {
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    display: flex;
    background-color: #121212;
}

.dashboard-container {
    display: flex;
    width: 100%; /* Make sure the container takes the full width */
}

.sidebar {
    position: fixed; /* Keep the sidebar fixed on the left */
    width: 200px; /* Fixed width for the sidebar */
    height: 100%; /* Full height */
    background-color: #1E1E1E; /* Sidebar background */
    color: white;
    padding: 20px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
    flex-shrink: 0; /* Prevent the sidebar from shrinking */
    overflow-y: auto; /* Allow scrolling if content overflows */
    transition: transform 0.3s ease;
    z-index: 2000;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.menu-item:hover {
    background-color: #03346E; /* Change background on hover */
}

.menu-item.active {
    background-color: #6EACDA; /* Highlight active menu item */
    color: black;
}

.content {
    margin-left: 210px; /* Push content to the right of the sidebar */
    padding: 20px;
    color: #fff;
    flex: 1; /* Allow content area to grow */
    overflow-y: auto; /* Allow scrolling if content overflows */
}

.sidebar.hidden {
    transform: translateX(-100%); /* Slide sidebar out of view */
}

.toggle-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #272727;
    color: white;
    border: 1px solid #414141;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    display: none;
    z-index: 9998;
}


@media screen and (max-width: 768px) {
    .toggle-button {
        display: block; 
    }
    .sidebar {
        width: 100%;
    }
    .sidebar.hidden {
        transform: translateX(-100%); /* Slide sidebar out of view */
    }
    .content {
        margin-left: 0;
    }
}

.app-name {
    font-size: 24px;
    margin-bottom: 30px;
    text-align: center;
}


h1 {
    margin-bottom: 20px;
    text-align: center; /* Center the text */
}

section {
    display: none; /* Hide all sections by default */
}

section.active {
    display: block; /* Show the active section */
}
.notifications-box {
    background-color: #202020; /* White background for notifications */
    border-radius: 8px; /* Rounded corners */
    padding: 20px; /* Padding around the content */
    margin-top: 20px; /* Space above the notifications box */
    max-height: 500px;
    overflow:auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

.notifications-box::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

.notifications-box::-webkit-scrollbar-track {
    background: #333333; /* Background color for the track (scrollbar track) */
    border-radius: 8px; /* Rounded corners for the track */
}

.notifications-box::-webkit-scrollbar-thumb {
    background-color: #fff; /* Color of the scrollbar thumb */
    border-radius: 8px; /* Rounded corners for the thumb */
    border: 2px solid #202020; /* Optional: Add padding inside the scrollbar thumb */
}

.notification-api {
    border-bottom: 1px solid #eee; /* Light grey line between notifications */
    padding: 10px 0; /* Padding for each notification */
    animation: fadeIn 0.5s ease;
}

.notification-api:last-child {
    border-bottom: none; /* Remove the line for the last notification */
}

.notification-api p {
    margin: 0; /* Remove margin from paragraph */
}

.notification-api small {
    color: #888; /* Light grey color for the date */
}


.custom-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: #202020;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.custom-table th, .custom-table td {
    padding: 15px;
    text-align: left;
}

.custom-table th {
    background-color: #03346E;
    color: white;
}

.custom-table td {
    background-color: #202020;
    border-bottom: 1px solid #333;
    color: #E0E0E0;
}

.custom-table tr:last-child td {
    border-bottom: none;
}

/* Aligning and styling text */
.custom-table td, .custom-table th {
    text-align: center;
}

.custom-table tbody tr:hover {
    background-color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .custom-table th, .custom-table td {
        font-size: 14px;
    }
}

.button {
    background-color: #333; /* Dark grey background */
    color: #fff; /* White text */
    padding: 12px 24px; /* Padding for the button */
    border: none; /* Remove borders */
    border-radius: 8px; /* Rounded corners */
    font-size: 16px; /* Font size */
    cursor: pointer; /* Change cursor on hover */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    transition: background-color 0.3s ease, transform 0.3s ease; /* Smooth transitions */
}

.button:hover {
    background-color: #555; /* Lighter grey on hover */
    transform: translateY(-2px); /* Slight lift on hover */
}

.button:active {
    background-color: #222; /* Even darker grey when active */
    transform: translateY(0); /* Reset the lift when pressed */
}

.profile-info {
    background-color: #1E1E1E;
    color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    text-align: right; /* Align text to the right for RTL */
}

.profile-info p {
    margin: 10px 0;
}

.profile-info strong {
    color: #6EACDA; /* Light blue for labels */
}
#searchBar {
    width: 100%;
    padding: 8px; /* Smaller padding */
    margin-bottom: 20px;
    border-radius: 20px; /* More rounded corners */
    border: 1px solid transparent; /* No border initially */
    background-color: #333; /* Dark background */
    color: #fff; /* White text color */
    font-size: 14px; /* Slightly smaller font */
    outline: none;
    transition: all 0.3s ease; /* Smooth transition for effects */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

#searchBar:hover {
    background-color: #444; /* Slightly lighter background on hover */
}

#searchBar:focus {
    border: 1px solid #6EACDA; /* Light blue border when focused */
    background-color: #1E1E1E; /* Darker background */
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15); /* More shadow on focus */
    transform: scale(1.02); /* Slight zoom effect */
}

.subject-button {
    display: block;
    width: 70%; /* Slightly reduced width for a compact feel */
    padding: 12px; /* Slightly reduced padding */
    margin: 10px auto;
    border: 2px solid #03346E;
    border-radius: 12px; /* More rounded corners */
    background-color: #202020; /* Dark background to match the page theme */
    color: #E2E2B6; /* Light beige text for readability */
    font-size: 16px; /* Reduced font size for a cleaner look */
    text-align: center; /* Center text alignment */
    cursor: pointer;
    transition: all 0.3s ease; /* Smooth transitions */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Light shadow for a bit of depth */
}

.subject-button:hover {
    border-color: #6EACDA;
    background-color: #6EACDA; /* Blue hover effect */
    color: white; /* White text on hover */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3); /* Enhanced shadow on hover */
    transform: translateY(-4px); /* Slight lift effect on hover */
}

.subject-button:active {
    transform: translateY(2px); /* Slight press effect on click */
}


.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    z-index: 9999;
}

.spinner {
    border: 8px solid rgba(255, 255, 255, 0.3);
    border-top: 8px solid #6EACDA;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


/* Modal container */
.modal {
    display: none; /* Hidden by default */
    z-index: 2002; /* Ensure it stays above everything */
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden; /* Disable the body scrollbar when modal is active */
    background-color: rgba(0, 0, 0, 0.8); /* Black with opacity */
    backdrop-filter: blur(5px); /* Blurs everything behind the modal */
}

/* Modal content */
.modal-content {
    background-color: #121212;
    margin: 5% auto; /* Slightly reduced margin */
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 10px;
    max-width: 600px; /* Make it responsive */
    max-height: 80vh; /* Modal content height is limited to viewport */
    overflow-y: auto; /* Enable internal scrollbar if content exceeds height */
    z-index: 2003; /* Ensure the modal content is above the blurred background */
}

/* Close button */
.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

/* Blur effect when modal is active */
body.modal-active {
    filter: blur(5px);
}

.homework-header {
    display: flex;
    justify-content: center;
    align-items: center;
}

.homework-card p {
    margin: 8px 0;
}

.homework-separator {
    border: none;
    border-top: 1px solid #fff;
    margin: 20px 0;
}

.homework-header h3 {
    text-align: center;
    font-size: 22px;
    margin-bottom: 20px;
    color: #fff;
    border-bottom: 2px solid #fff;
    padding-bottom: 10px;
}

.homework-card {
    background-color: #202020;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 16px;
    color: #fff;
}

.settings-container {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3);
    font-size: 16px;
    color: #fff;
    display: flex;
    justify-content: space-between; 
    align-items: center;
}