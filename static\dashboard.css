/* Dashboard Layout */
body {
    margin: 0;
    display: flex;
    min-height: 100vh;
    position: relative;
}

.dashboard-container {
    display: flex;
    width: 100%;
    min-height: 100vh;
    position: relative;
}

/* Futuristic Sidebar */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-right: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: 32px 0;
    overflow-y: auto;
    transition: transform var(--transition-medium);
    z-index: 1000;
    box-shadow: 4px 0 24px var(--shadow-light);
}

/* App Name */
.app-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 24px 40px;
    text-align: center;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    padding-bottom: 16px;
}

.app-name::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

/* Menu Items */
.menu-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    margin: 4px 16px;
    cursor: pointer;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-medium);
    position: relative;
    font-weight: 500;
    color: var(--text-secondary);
    overflow: hidden;
    gap: 12px;
}

.menu-item i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
    margin-right: 8px;
}

.menu-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--accent-primary);
    transform: scaleY(0);
    transition: transform var(--transition-medium);
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    transform: translateX(4px);
}

.menu-item:hover::before {
    transform: scaleY(1);
}

.menu-item.active {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(124, 58, 237, 0.1));
    color: var(--text-primary);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.2);
}

.menu-item.active::before {
    transform: scaleY(1);
    background: var(--gradient-secondary);
    width: 4px;
}

/* Content Area */
.content {
    margin-left: 280px;
    padding: 32px;
    color: var(--text-primary);
    flex: 1;
    overflow-y: auto;
    min-height: 100vh;
    background: var(--primary-bg);
    position: relative;
}

/* Sidebar Hidden State */
.sidebar.hidden {
    transform: translateX(-100%);
}

/* Toggle Button */
.toggle-button {
    position: fixed;
    bottom: 24px;
    left: 24px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all var(--transition-medium);
    z-index: 9998;
    box-shadow: 0 8px 24px var(--shadow-medium);
}

.toggle-button:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 32px var(--shadow-medium);
}

.toggle-button:active {
    transform: scale(0.95);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .toggle-button {
        display: flex;
    }

    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .sidebar:not(.hidden) {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
        padding: 24px 16px;
    }
}


/* Section Styles */
h1 {
    margin-bottom: 32px;
    text-align: center;
    font-size: 2.25rem;
    font-weight: 700;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

section {
    display: none;
    animation: fadeIn 0.5s ease-out;
}

section.active {
    display: block;
}

.center {
    text-align: center;
}
.notifications-box {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    margin-top: 24px;
    max-height: 500px;
    overflow: auto;
    box-shadow: 0 12px 40px var(--shadow-medium);
    position: relative;
}

.notifications-box::-webkit-scrollbar {
    width: 6px;
}

.notifications-box::-webkit-scrollbar-track {
    background: transparent;
    border-radius: var(--border-radius-sm);
}

.notifications-box::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--border-radius-sm);
}

.notification-api {
    border-bottom: 1px solid var(--glass-border);
    padding: 16px 0;
    animation: slideUp 0.5s ease;
    transition: all var(--transition-medium);
}

.notification-api:hover {
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius-sm);
    padding: 16px 12px;
    margin: 0 -12px;
}

.notification-api:last-child {
    border-bottom: none;
}

.notification-api p {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.5;
}

.notification-api small {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 400;
}


.custom-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 24px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 12px 40px var(--shadow-medium);
}

.custom-table th, .custom-table td {
    padding: 16px 20px;
    text-align: center;
    border-bottom: 1px solid var(--glass-border);
}

.custom-table th {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.custom-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.custom-table th i {
    margin-right: 8px;
    font-size: 0.9rem;
}

.custom-table td {
    background: transparent;
    color: var(--text-primary);
    font-weight: 400;
    transition: all var(--transition-medium);
}

.custom-table tbody tr {
    transition: all var(--transition-medium);
}

.custom-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.03);
    transform: scale(1.01);
}

.custom-table tbody tr:hover td {
    color: var(--accent-primary);
}

.custom-table tr:last-child td {
    border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .custom-table th, .custom-table td {
        font-size: 14px;
        padding: 12px 16px;
    }
}

.button {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.button:hover::before {
    left: 100%;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 212, 255, 0.3);
}

.button:active {
    transform: translateY(0);
}

.button i {
    margin-right: 8px;
    font-size: 1rem;
}

.profile-info {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    margin: 24px auto 0;
    box-shadow: 0 12px 40px var(--shadow-medium);
    width: 100%;
    max-width: 500px;
    text-align: right;
}

.profile-info p {
    margin: 16px 0;
    color: var(--text-primary);
    font-size: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--glass-border);
}

.profile-info p:last-child {
    border-bottom: none;
}

.profile-info strong {
    color: var(--accent-primary);
    font-weight: 600;
    min-width: 140px;
}

.profile-info strong i {
    margin-right: 8px;
    font-size: 1rem;
}

.profile-info span {
    color: var(--text-secondary);
    font-weight: 500;
}
#searchBar {
    width: 100%;
    padding: 16px 20px;
    margin-bottom: 24px;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: var(--text-primary);
    font-size: 16px;
    outline: none;
    transition: all var(--transition-medium);
    box-shadow: 0 8px 24px var(--shadow-light);
    font-family: inherit;
}

#searchBar::placeholder {
    color: var(--text-muted);
}

#searchBar:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-primary);
}

#searchBar:focus {
    border-color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.1);
    box-shadow:
        0 0 0 3px rgba(0, 212, 255, 0.1),
        0 12px 32px var(--shadow-medium);
    transform: translateY(-2px);
}

.subject-button {
    display: block;
    width: 80%;
    padding: 16px 24px;
    margin: 16px auto;
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    box-shadow: 0 8px 24px var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.subject-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    opacity: 0;
    transition: all 0.6s ease;
    z-index: -1;
}

.subject-button:hover::before {
    left: 0;
    opacity: 0.1;
}

.subject-button:hover {
    border-color: var(--accent-primary);
    color: var(--accent-primary);
    box-shadow: 0 12px 32px var(--shadow-medium);
    transform: translateY(-4px);
}

.subject-button:active {
    transform: translateY(-2px);
}


.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    z-index: 9999;
}

.spinner {
    border: 8px solid rgba(255, 255, 255, 0.3);
    border-top: 8px solid #6EACDA;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


/* Modal container */
.modal {
    display: none;
    z-index: 10000;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
}

/* Modal content */
.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid var(--glass-border);
    margin: 5% auto;
    padding: 32px;
    width: 90%;
    border-radius: var(--border-radius-xl);
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px var(--shadow-heavy);
    position: relative;
    animation: scaleIn 0.4s ease;
}

/* Close button */
.close {
    color: var(--text-muted);
    float: right;
    font-size: 32px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: -8px -8px 0 0;
}

.close:hover,
.close:focus {
    color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

/* Blur effect when modal is active */
body.modal-active {
    filter: blur(5px);
}

.homework-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
}

.homework-header h3 {
    text-align: center;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    padding-bottom: 12px;
}

.homework-header h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

.homework-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 24px var(--shadow-light);
    font-size: 16px;
    color: var(--text-primary);
    transition: all var(--transition-medium);
}

.homework-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px var(--shadow-medium);
}

.homework-card p {
    margin: 12px 0;
    line-height: 1.5;
}

.homework-card strong {
    color: var(--accent-primary);
    font-weight: 600;
}

.homework-separator {
    border: none;
    border-top: 1px solid var(--glass-border);
    margin: 24px 0;
    opacity: 0.5;
}

.settings-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    margin-top: 24px;
    box-shadow: 0 12px 40px var(--shadow-medium);
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.settings-container .button {
    margin: 0;
    width: 100%;
}

/* Additional Components */
.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius-lg);
}

.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 24px;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .content {
        margin-left: 0;
        padding: 24px 20px;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar:not(.hidden) {
        transform: translateX(0);
    }

    .toggle-button {
        bottom: 16px;
        left: 16px;
    }
}

@media (max-width: 768px) {
    .subjects-grid {
        grid-template-columns: 1fr;
    }

    .custom-table {
        font-size: 14px;
    }

    .custom-table th,
    .custom-table td {
        padding: 12px 8px;
    }

    h1 {
        font-size: 1.75rem;
    }
}

@media (max-width: 480px) {
    .content {
        padding: 16px 12px;
    }

    .profile-info {
        padding: 16px;
    }

    .profile-info p {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .settings-container {
        padding: 16px;
    }

    .notifications-box {
        padding: 16px;
    }

    .menu-item {
        padding: 14px 20px;
        margin: 2px 12px;
    }

    .menu-item i {
        font-size: 1rem;
        margin-right: 6px;
    }

    .app-name {
        font-size: 1.25rem;
        margin-bottom: 32px;
    }

    .toggle-button {
        width: 48px;
        height: 48px;
        bottom: 12px;
        left: 12px;
    }
}