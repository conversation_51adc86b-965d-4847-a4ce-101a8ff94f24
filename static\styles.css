/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* CSS Custom Properties for Theme System */
:root {
    /* Dark Theme (Default) */
    --primary-bg: #0a0a0f;
    --secondary-bg: #1a1a2e;
    --tertiary-bg: #16213e;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #8892b0;
    --accent-primary: #00d4ff;
    --accent-secondary: #7c3aed;
    --accent-tertiary: #f59e0b;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --shadow-light: rgba(0, 212, 255, 0.1);
    --shadow-medium: rgba(0, 212, 255, 0.2);
    --shadow-heavy: rgba(0, 0, 0, 0.5);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
    --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Light Theme */
[data-theme="light"] {
    --primary-bg: #f8fafc;
    --secondary-bg: #ffffff;
    --tertiary-bg: #f1f5f9;
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(0, 0, 0, 0.1);
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --accent-primary: #0ea5e9;
    --accent-secondary: #8b5cf6;
    --accent-tertiary: #f59e0b;
    --success: #059669;
    --warning: #d97706;
    --error: #dc2626;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.25);
}

/* Global Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: background var(--transition-medium), color var(--transition-medium);
    position: relative;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--secondary-bg);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--accent-secondary), var(--accent-primary));
}

/* Glassmorphism Utility Classes */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px var(--shadow-light);
}

.glass-strong {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 40px var(--shadow-medium);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

.text-gradient {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animation Utilities */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px var(--shadow-medium);
}

.hover-glow {
    transition: box-shadow var(--transition-medium);
}

.hover-glow:hover {
    box-shadow: 0 0 30px var(--accent-primary);
}

/* Notification System */
#notification-container {
    position: fixed;
    top: 24px;
    right: 24px;
    width: 320px;
    z-index: 10000;
    pointer-events: none;
}

.notification {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    padding: 16px 20px;
    margin-bottom: 12px;
    color: var(--text-primary);
    box-shadow: 0 8px 32px var(--shadow-medium);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    animation: slideInRight 0.4s ease-out, fadeOut 0.4s 4.6s ease-out forwards;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-primary);
}

.notification.success::before {
    background: var(--success);
}

.notification.warning::before {
    background: var(--warning);
}

.notification.error::before {
    background: var(--error);
}

.notification .notification-content {
    flex: 1;
    margin-right: 12px;
}

.notification .notification-content strong {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.notification .notification-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

.notification .close-btn {
    cursor: pointer;
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--text-muted);
    transition: color var(--transition-fast);
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.notification .close-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 24px;
    left: 24px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all var(--transition-medium);
    z-index: 9999;
    box-shadow: 0 4px 16px var(--shadow-light);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 24px var(--shadow-medium);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-medium), visibility var(--transition-medium);
}

.loading-spinner.active {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 3px solid transparent;
    border-top: 3px solid var(--accent-primary);
    border-right: 3px solid var(--accent-secondary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
}

.spinner::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid transparent;
    border-top: 3px solid var(--accent-tertiary);
    border-radius: 50%;
    animation: spin 2s linear infinite reverse;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    #notification-container {
        top: 16px;
        right: 16px;
        left: 16px;
        width: auto;
    }

    .theme-toggle {
        top: 16px;
        left: 16px;
        width: 44px;
        height: 44px;
        font-size: 1.125rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
}
