::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

::-webkit-scrollbar-track {
    background: #333333; /* Background color for the track (scrollbar track) */
    border-radius: 8px; /* Rounded corners for the track */
}

::-webkit-scrollbar-thumb {
    background-color: #fff; /* Color of the scrollbar thumb */
    border-radius: 8px; /* Rounded corners for the thumb */
    border: 2px solid #202020; /* Optional: Add padding inside the scrollbar thumb */
}

body {
    background-color: #121212;
    font-family: Arial, sans-serif;
    color: #E0E0E0;
}

#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    z-index: 1000;
}

.notification {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: fadeIn 0.5s ease, fadeOut 0.5s 4.5s ease forwards;
}

.notification.success {
    background-color: #4CAF50; /* Green */
}

.notification.warning {
    background-color: #FFC107; /* Yellow/Orange */
}

.notification.error {
    background-color: #F44336; /* Red */
}

.notification .close-btn {
    cursor: pointer;
    font-weight: bold;
    padding-left: 10px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; transform: translateY(-20px); }
}
