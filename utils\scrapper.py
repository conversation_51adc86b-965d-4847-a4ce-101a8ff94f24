import tls_client
import json
from bs4 import BeautifulSoup
import pandas as pd

session = tls_client.Session(
    client_identifier="chrome112",
    random_tls_extension_order=True

)
def mlazemna_scrapper():
    url = "https://mlazemna.com/"
    response = session.get(url)
    if response.status_code == 200:
        soup = BeautifulSoup(response.text, 'html.parser')
        container_wrappers = soup.find_all('div', class_='container-wrapper')
        result = {}
        for container in container_wrappers:
            title_tag = container.find('div', class_='mag-box-title the-global-title').find('h3')
            if title_tag:
                title = title_tag.get_text(strip=True)
            if title not in result:
                result[title] = []
            for link in container.find_all('a', href=True):
                link_url = link['href']
                link_title = link.get_text(strip=True)
                result[title].append({
                    'title': link_title,
                    'url': link_url
                })
        result.pop('WhatsApp', None)
        result.pop('Telegram', None)
        result_json = json.dumps(result, ensure_ascii=False, indent=4)
        return result_json