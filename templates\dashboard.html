<!DOCTYPE html>
<html lang="en" data-theme="dark">
   <head>
<script async src="https://www.googletagmanager.com/gtag/js?id=G-S55EPWPGH7"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-S55EPWPGH7');
</script>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Dashboard - Al Manar مدرسة المنار الاهلية</title>
      <meta name="description" content="لوحة تحكم مدرسة المنار الاهلية - إدارة الطلاب والدرجات والواجبات">
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
      <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
      <link rel="stylesheet" href="{{ url_for('static', filename='dashboard.css') }}">
      <script src="{{ url_for('static', filename='dashboard.js') }}"></script>
   </head>
   <body class="fade-in">
      <!-- Theme Toggle Button -->
      <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle theme">
         <i class="fas fa-moon" id="theme-icon"></i>
      </button>

      <!-- Loading Spinner -->
      <div id="loading-spinner" class="loading-spinner">
         <div class="spinner"></div>
      </div>

      <!-- Homework Modal -->
      <div id="homeworkModal" class="modal">
         <div class="modal-content">
            <span class="close">&times;</span>
            <div id="homeworkContent"></div>
         </div>
      </div>

      <!-- Notification Container -->
      <div id="notification-container"></div>

      <!-- Dashboard Container -->
      <div class="dashboard-container">
         <!-- Sidebar -->
         <nav class="sidebar" id="sidebar" role="navigation" aria-label="Main navigation">
            <div class="app-name">مدرسة المنار الاهلية</div>
            <div class="menu-item active hover-lift" data-section="home" role="button" tabindex="0">
               <i class="fas fa-home" style="margin-left: 8px;"></i>
               القائمة الرئيسية
            </div>
            <div class="menu-item hover-lift" data-section="grades" role="button" tabindex="0">
               <i class="fas fa-chart-line" style="margin-left: 8px;"></i>
               الدرجات
            </div>
            <div class="menu-item hover-lift" data-temp="schedule" role="button" tabindex="0">
               <i class="fas fa-calendar-alt" style="margin-left: 8px;"></i>
               الجدول الاسبوعي
            </div>
            <div class="menu-item hover-lift" data-section="homework" role="button" tabindex="0">
               <i class="fas fa-book" style="margin-left: 8px;"></i>
               الواجبات
            </div>
            <div class="menu-item hover-lift" data-temp="teachers" role="button" tabindex="0">
               <i class="fas fa-chalkboard-teacher" style="margin-left: 8px;"></i>
               المدرسين والمواد
            </div>
            <div class="menu-item hover-lift" data-section="absences" role="button" tabindex="0">
               <i class="fas fa-user-times" style="margin-left: 8px;"></i>
               الغيابات
            </div>
            <div class="menu-item hover-lift" data-section="exams" role="button" tabindex="0">
               <i class="fas fa-clipboard-list" style="margin-left: 8px;"></i>
               جدول الامتحانات
            </div>
            <div class="menu-item hover-lift" data-section="studentprofile" role="button" tabindex="0">
               <i class="fas fa-user" style="margin-left: 8px;"></i>
               معلومات الطالب
            </div>
            <div class="menu-item hover-lift" data-section="settings" role="button" tabindex="0">
               <i class="fas fa-cog" style="margin-left: 8px;"></i>
               الاعدادات
            </div>
         </nav>

         <!-- Mobile Toggle Button -->
         <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()" aria-label="Toggle sidebar">
            <i class="fas fa-bars"></i>
         </button>

         <!-- Main Content -->
         <main class="content" role="main">
            <!-- Home Section -->
            <section id="home" class="active">
               <h1 class="center text-gradient">الأشعارات</h1>
               <div id="notifications-box" class="notifications-box glass hover-lift"></div>
            </section>

            <!-- Absences Section -->
            <section id="absences">
               <h1 class="center text-gradient">الغيابات</h1>
               <div class="table-container">
                  <table class="custom-table glass hover-lift">
                     <thead>
                        <tr>
                           <th><i class="fas fa-sticky-note" style="margin-left: 8px;"></i>ملاحظة</th>
                           <th><i class="fas fa-book" style="margin-left: 8px;"></i>اسم المادة</th>
                           <th><i class="fas fa-calendar" style="margin-left: 8px;"></i>تاريخ الغياب</th>
                           <th><i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>نوع الغياب</th>
                        </tr>
                     </thead>
                     <tbody id="absenceTableBody">
                     </tbody>
                  </table>
               </div>
            </section>

            <!-- Exams Section -->
            <section id="exams">
               <h1 class="center text-gradient">جدول الامتحانات</h1>
               <div class="table-container">
                  <table class="custom-table glass hover-lift">
                     <thead>
                        <tr>
                           <th><i class="fas fa-calendar-year" style="margin-left: 8px;"></i>السنة</th>
                           <th><i class="fas fa-calendar-month" style="margin-left: 8px;"></i>الشهر</th>
                           <th><i class="fas fa-calendar-day" style="margin-left: 8px;"></i>اليوم</th>
                           <th><i class="fas fa-book" style="margin-left: 8px;"></i>اسم المادة</th>
                        </tr>
                     </thead>
                     <tbody id="ExamsScheduleTableBody">
                     </tbody>
                  </table>
               </div>
            </section>

            <!-- Homework Section -->
            <section id="homework">
               <h1 class="center text-gradient">الواجبات</h1>
               <input class="center" type="text" id="searchBar" placeholder="🔍 بحث عن اسم المادة" oninput="filterSubjects()">
               <div id="subjectsContainer" class="subjects-grid"></div>
            </section>

            <!-- Student Profile Section -->
            <section id="studentprofile">
               <h1 class="center text-gradient">معلومات الطالب</h1>
               <div class="profile-info glass hover-lift">
                  <p><strong><i class="fas fa-user" style="margin-left: 8px;"></i>الاسم:</strong> <span id="studentName"></span></p>
                  <p><strong><i class="fas fa-venus-mars" style="margin-left: 8px;"></i>النوع:</strong> <span id="studentSex"></span></p>
                  <p><strong><i class="fas fa-graduation-cap" style="margin-left: 8px;"></i>المرحلة الدراسية:</strong> <span id="studentStage"></span></p>
                  <p><strong><i class="fas fa-code-branch" style="margin-left: 8px;"></i>الفرع:</strong> <span id="studentBranch"></span></p>
                  <p><strong><i class="fas fa-key" style="margin-left: 8px;"></i>كلمة السر:</strong> <span id="studentpassword"></span></p>
               </div>
            </section>

            <!-- Grades Section -->
            <section id="grades">
               <h1 class="center text-gradient">الدرجات</h1>
               <div class="table-container">
                  <table class="custom-table glass hover-lift">
                     <thead>
                        <tr>
                           <th><i class="fas fa-star" style="margin-left: 8px;"></i>الدرجة</th>
                           <th><i class="fas fa-book" style="margin-left: 8px;"></i>اسم المادة</th>
                           <th><i class="fas fa-calendar-month" style="margin-left: 8px;"></i>الشهر</th>
                           <th><i class="fas fa-calendar-year" style="margin-left: 8px;"></i>السنة</th>
                        </tr>
                     </thead>
                     <tbody id="GradesTableBody">
                     </tbody>
                  </table>
               </div>
            </section>

            <!-- Settings Section -->
            <section id="settings">
               <h1 class="center text-gradient">الاعدادات</h1>
               <div class="settings-container glass hover-lift">
                  <button class="button hover-glow" onclick="logout()">
                     <i class="fas fa-sign-out-alt" style="margin-left: 8px;"></i>
                     تسجيل الخروج
                  </button>
                  <input type="file" id="wallpaper-upload" style="display: none;" accept="image/*">
                  <button class="button hover-glow" id="apply-wallpaper">
                     <i class="fas fa-image" style="margin-left: 8px;"></i>
                     تغيير الخلفية
                  </button>
                  <button class="button hover-glow" onclick="toggleTheme()">
                     <i class="fas fa-palette" style="margin-left: 8px;"></i>
                     تغيير المظهر
                  </button>
               </div>
            </section>
         </main>
      </div>

      <script>
         // Theme toggle functionality
         function toggleTheme() {
            const html = document.documentElement;
            const themeIcon = document.getElementById('theme-icon');
            const currentTheme = html.getAttribute('data-theme');
            
            if (currentTheme === 'dark') {
               html.setAttribute('data-theme', 'light');
               themeIcon.className = 'fas fa-sun';
               localStorage.setItem('theme', 'light');
            } else {
               html.setAttribute('data-theme', 'dark');
               themeIcon.className = 'fas fa-moon';
               localStorage.setItem('theme', 'dark');
            }
         }

         // Initialize theme
         function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            const html = document.documentElement;
            const themeIcon = document.getElementById('theme-icon');
            
            html.setAttribute('data-theme', savedTheme);
            themeIcon.className = savedTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
         }

         // Initialize theme when DOM is loaded
         document.addEventListener('DOMContentLoaded', function() {
            initTheme();
         });
      </script>
   </body>
</html>
