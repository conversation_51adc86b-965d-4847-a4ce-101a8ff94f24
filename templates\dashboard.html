<!DOCTYPE html>
<html lang="en">
   <head>
<script async src="https://www.googletagmanager.com/gtag/js?id=G-S55EPWPGH7"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-S55EPWPGH7');
</script>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Al manar</title>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
      <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
      <link rel="stylesheet" href="{{ url_for('static', filename='dashboard.css') }}">
      <script src="{{ url_for('static', filename='dashboard.js') }}"></script>
   </head>
   <body>
      <div id="loading-spinner" class="loading-spinner">
         <div class="spinner"></div>
      </div>
      <div id="homeworkModal" class="modal">
         <div class="modal-content">
            <span class="close">&times;</span>
            <div id="homeworkContent"></div>
         </div>
      </div>
      <div id="notification-container"></div>
      <div class="dashboard-container">
         <div class="sidebar" id="sidebar">
            <div class="app-name">مدرسة المنار الاهلية</div>
            <div class="menu-item active" data-section="home">القائمة الرئيسية</div>
            <div class="menu-item" data-section="grades">الدرجات</div>
            <div class="menu-item" data-temp="schedule">الجدول الاسبوعي</div>
            <div class="menu-item" data-section="homework">الواجبات</div>
            <div class="menu-item" data-temp="teachers">المدرسين والمواد</div>
            <div class="menu-item" data-section="absences">الغيابات</div>
            <div class="menu-item" data-section="exams">جدول الامتحانات</div>
            <div class="menu-item" data-section="studentprofile">معلومات الطالب</div>
            <div class="menu-item" data-section="settings">الاعدادات</div>
         </div>
         <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()">☰</button>
         <main class="content">
            <section id="home" class="active">
               <h1 class="center">الأشعارات</h1>
               <div id="notifications-box" class="notifications-box"></div>
               <br>
            </section>
            <section id="absences" class="">
               <h1 class="center">الغيابات</h1>
               <table class="custom-table">
                  <thead>
                     <tr>
                        <th>ملاحظة</th>
                        <th>اسم المادة</th>
                        <th>تاريخ الغياب</th>
                        <th>نوع الغياب</th>
                     </tr>
                  </thead>
                  <tbody id="absenceTableBody">
                  </tbody>
               </table>
               <br>
            </section>
            <section id="exams" class="">
               <h1 class="center">جدول الامتحانات</h1>
               <table class="custom-table">
                  <thead>
                     <tr>
                        <th>السنة</th>
                        <th>الشهر</th>
                        <th>اليوم</th>
                        <th>اسم المادة</th>
                     </tr>
                  </thead>
                  <tbody id="ExamsScheduleTableBody">
                  </tbody>
               </table>
               <br>
            </section>
            <section id="homework" class="">
               <h1 class="center">الواجبات</h1>
               <input class="center" type="text" id="searchBar" placeholder="بحث عن اسم المادة" oninput="filterSubjects()">
               <div id="subjectsContainer"></div>
               <br>
            </section>
            <section id="studentprofile" class="">
               <h1 class="center">معلومات الطالب</h1>
               <div class="profile-info">
                  <p><strong>الاسم:</strong> <span id="studentName"></span></p>
                  <p><strong>النوع:</strong> <span id="studentSex"></span></p>
                  <p><strong>المرحلة الدراسية:</strong> <span id="studentStage"></span></p>
                  <p><strong>الفرع:</strong> <span id="studentBranch"></span></p>
                  <p><strong>كلمة السر:</strong> <span id="studentpassword"></span></p>
               </div>
               <br>
            </section>
            <section id="grades" class="">
               <h1 class="center">الدرجات</h1>
               <table class="custom-table">
                  <thead>
                     <tr>
                        <th>الدرجة</th>
                        <th>اسم المادة</th>
                        <th>الشهر</th>
                        <th>السنة</th>
                     </tr>
                  </thead>
                  <tbody id="GradesTableBody">
                  </tbody>
               </table>
               <br>
            </section>
            <section id="settings" class="">
               <h1 class="center">الاعدادات</h1>
               <div class="settings-container">
                  <button class="button" onclick="logout()">تسجيل الخروج</button>
                  <input type="file" id="wallpaper-upload" style="display: none;" accept="image/*">
                  <button class="button" id="apply-wallpaper">تغيير الخلفية</button>
               </div>
               <br>
            </section>
         </main>
      </div>
   </body>
</html>