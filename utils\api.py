import requests
import json
import os
import random
import time
BASE_API = "https://manarsystem-001-site1.atempurl.com"

headers = {
    "Accept": "application/json",
    "Content-Type": "application/json; charset=utf-8",
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 7.1.2; SM-G977N Build/LMY48Z)",
}


def login(username, password):
    url = BASE_API + "/Account/Login"
    payload = {
    "PersonId": 0,
    "TypePersonId": 0, 
    "SexId": None,
    "Age": None,
    "Name": None,
    "Email": None,
    "Phone1": None,
    "Phone2": None,
    "Image": None,
    "UserName": None,
    "Password": None,
    "IsActive": False,
    "IsDeleted": False,
    "SexName": None,
    "Br_St_Id": 0,
    "BranchName": None,
    "StageName": None,
    "Salary": 0,
    "Token": None
    }
    payload['UserName'] = username
    payload['Password'] = password
    lol = requests.post(url, headers=headers, data=json.dumps(payload))
    return lol.json()

def notifications(stid, token):
    url = BASE_API + "/Account/GetNotificationStudent?StudentId="+str(stid)
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetAbsenceForStudent(stid, token):
    url = BASE_API + "/Absence/GetAbsenceForStudent?StudentId="+str(stid)
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetProfileStudent(stid, token):
    url = BASE_API + "/Account/GetProfileStudent?Id="+str(stid)
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetAllTypeHomework(token):
    url = BASE_API + "/LookUps/GetAll?Type=Item"
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetDutiesByStudentIdAndItemId(stid, itemid, token):
    url = BASE_API + "/Duties/GetDutiesByStudentIdAndItemId?ItemId="+str(itemid)+"&StudenId="+str(stid)
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetExamsByStudentId(stid, token):
    url = BASE_API + "/Exams/GetExamsByStudent?Id="+str(stid)+"&Month=%20"
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetAllCarousel(token):
    url = BASE_API + "/Carousel/GetAll"
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def GetDegreesForStudent(stid, token):
    url = BASE_API + "/Degrees/GetDegreesForStudent?Id="+str(stid)+"&YearName=%20"
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    return lol.json()

def checktoken(stid, token):
    lol = ["1901", "53", "583", "1721", "1733", "702", "708"]
    url = BASE_API + f"/Account/GetProfileStudent?Id={stid}"
    headers['Authorization'] = 'Bearer ' + token
    lol = requests.get(url, headers=headers)
    if lol.status_code == 200 and lol.json()['success'] == True:
        return True
    else:
        return False