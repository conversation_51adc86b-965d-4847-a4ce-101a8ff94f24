body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    overflow: hidden;
    flex-direction: column;
    margin: 0;  /* To remove default margin in body */
}

.login-container * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.login-container {
    background-color: #1E1E1E;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0px 0px 10px #6EACDA;
    text-align: center;
    width: 300px; /* Default width */
}

.login-title {
    color: #6EACDA;
    margin-bottom: 20px;
    font-size: 24px;
}

.login-form input {
    background-color: #2A2A2A;
    border: none;
    outline: none;
    padding: 12px;
    margin: 10px 0;
    width: 100%;
    color: white;
    font-size: 16px;
    border-radius: 5px;
}

.login-form button {
    background-color: #03346E;
    border: none;
    padding: 12px;
    width: 100%;
    color: white;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.login-form button:hover {
    background-color: #E2E2B6;
    color: black;
}

.login-form input::placeholder {
    color: #888888;
}

.password-container {
    position: relative;
    width: 100%;
}

.password-container input {
    width: 100%;
    padding-right: 40px;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.toggle-password i {
    font-size: 20px;
    color: #888888;
    transition: transform 0.3s ease, color 0.3s ease;
}

.toggle-password i:hover {
    color: #E2E2B6;
}

.eye-open-animation {
    opacity: 1;
}

.eye-close-animation {
    opacity: 0.6;
}

/* Media Query for mobile screens */
@media (max-width: 480px) {
    body {
        padding: 10px;  /* Add padding to body for smaller screens */
    }

    .login-container {
        width: 90%;  /* Set the container width to 90% on small screens */
        padding: 20px; /* Reduce padding for smaller devices */
    }

    .login-title {
        font-size: 20px; /* Adjust the font size of the title */
        margin-bottom: 15px;
    }

    .login-form input {
        font-size: 14px;  /* Make inputs smaller */
        padding: 10px;  /* Adjust padding to fit better */
    }

    .login-form button {
        font-size: 14px;  /* Adjust button text size */
        padding: 10px;  /* Adjust button padding */
    }

    .password-container input {
        font-size: 14px;  /* Make the password input smaller */
    }

    .toggle-password i {
        font-size: 18px;  /* Smaller icon size */
    }
}

/* Media Query for very small devices like portrait phones */
@media (max-width: 320px) {
    .login-container {
        width: 90%;  /* Use a larger percentage for very small devices */
        padding: 15px;  /* Further reduce padding */
    }

    .login-title {
        font-size: 18px; /* Further reduce title font size */
    }

    .login-form input {
        font-size: 14px;
    }

    .login-form button {
        font-size: 14px;
    }
}

#logged-in-box {
    display: none;
    background-color: #2A2A2A;
    padding: 10px;
    margin-top: 20px;
    border-radius: 10px;
    text-align: center;
    width: 370px;  /* Default width */
    box-shadow: 0px 0px 10px #6EACDA;
    cursor: pointer; /* Make the div clickable */
}

#logged-in-box p {
    color: white;
    font-size: 16px;
    margin-bottom: 10px;
}

/* Media Query for mobile screens */
@media (max-width: 480px) {
    #logged-in-box {
        width: 90%;  /* Adjust the width to 90% on smaller screens */
        padding: 10px;
    }

    #logged-in-box p {
        font-size: 14px;  /* Reduce font size for mobile screens */
    }
}

/* Media Query for very small devices (portrait phones) */
@media (max-width: 320px) {
    #logged-in-box {
        width: 90%;  /* Use a larger percentage for very small devices */
        padding: 8px;  /* Reduce padding further */
    }

    #logged-in-box p {
        font-size: 14px;  /* Smaller font size */
    }
}
