/* Login Page Specific Styles */
body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    flex-direction: column;
    position: relative;
    overflow-x: hidden;
}

/* Floating Particles Background */
.login-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    opacity: 0.6;
}

.particle:nth-child(2n) {
    background: var(--accent-secondary);
    animation-duration: 8s;
    animation-delay: -2s;
}

.particle:nth-child(3n) {
    background: var(--accent-tertiary);
    animation-duration: 10s;
    animation-delay: -4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    50% {
        transform: translateY(-100vh) rotate(180deg);
        opacity: 0.8;
    }
}

/* Login Container */
.login-container {
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 48px 40px;
    text-align: center;
    width: 100%;
    max-width: 420px;
    box-shadow:
        0 20px 60px var(--shadow-heavy),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    animation: scaleIn 0.6s ease-out;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Login Title */
.login-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 32px;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    line-height: 1.2;
}

.login-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.login-form input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    padding: 16px 20px;
    font-size: 16px;
    color: var(--text-primary);
    transition: all var(--transition-medium);
    outline: none;
    font-family: inherit;
}

.login-form input:focus {
    border-color: var(--accent-primary);
    background: rgba(255, 255, 255, 0.08);
    box-shadow:
        0 0 0 3px rgba(0, 212, 255, 0.1),
        0 8px 24px var(--shadow-light);
    transform: translateY(-2px);
}

.login-form input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

/* Password Container */
.password-container {
    position: relative;
    width: 100%;
}

.password-container input {
    padding-right: 56px;
}

.toggle-password {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all var(--transition-fast);
}

.toggle-password:hover {
    background: rgba(255, 255, 255, 0.1);
}

.toggle-password i {
    font-size: 18px;
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.toggle-password:hover i {
    color: var(--accent-primary);
}

/* Submit Button */
.login-form button {
    background: var(--gradient-secondary);
    border: none;
    border-radius: var(--border-radius-md);
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    cursor: pointer;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    margin-top: 8px;
}

.login-form button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.login-form button:hover::before {
    left: 100%;
}

.login-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 212, 255, 0.3);
}

.login-form button:active {
    transform: translateY(0);
}

/* Logged In Box */
#logged-in-box {
    display: none;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 20px 24px;
    margin-top: 24px;
    text-align: center;
    width: 100%;
    max-width: 420px;
    box-shadow: 0 12px 40px var(--shadow-medium);
    cursor: pointer;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

#logged-in-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-secondary);
    opacity: 0;
    transition: opacity var(--transition-medium);
    z-index: -1;
}

#logged-in-box:hover::before {
    opacity: 0.1;
}

#logged-in-box:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px var(--shadow-medium);
}

#logged-in-box p {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 500;
    margin: 0;
}

#logged-in-box span {
    color: var(--accent-primary);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 480px) {
    body {
        padding: 16px;
    }

    .login-container {
        padding: 32px 24px;
        max-width: 100%;
    }

    .login-title {
        font-size: 1.75rem;
        margin-bottom: 24px;
    }

    .login-form {
        gap: 16px;
    }

    .login-form input {
        padding: 14px 16px;
        font-size: 15px;
    }

    .password-container input {
        padding-right: 48px;
    }

    .toggle-password {
        right: 12px;
    }

    .login-form button {
        padding: 14px 20px;
        font-size: 15px;
    }

    #logged-in-box {
        padding: 16px 20px;
        margin-top: 20px;
    }

    #logged-in-box p {
        font-size: 15px;
    }
}

@media (max-width: 320px) {
    .login-container {
        padding: 24px 20px;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .login-form input {
        padding: 12px 14px;
        font-size: 14px;
    }

    .login-form button {
        padding: 12px 18px;
        font-size: 14px;
    }
}

/* Animation for eye icon */
.eye-open-animation {
    animation: eyeOpen 0.3s ease;
}

.eye-close-animation {
    animation: eyeClose 0.3s ease;
}

@keyframes eyeOpen {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes eyeClose {
    0% { transform: scale(1); }
    50% { transform: scale(0.8); }
    100% { transform: scale(1); }
}
