const notificationQueue = {
    success: [],
    warning: [],
    error: []
};
function logout() {
    for (let i = localStorage.length - 1; i >= 0; i--) { if (localStorage.key(i) !== 'wallpaper') localStorage.removeItem(localStorage.key(i)); };window.location.reload();
}
function togglePasswordVisibility() {
    const passwordInput = document.getElementById("password");
    const passwordEye = document.getElementById("password-eye");
    if (passwordInput.type === "password") {
        passwordInput.type = "text";
        passwordEye.classList.remove("fa-eye-slash");
        passwordEye.classList.add("fa-eye");
        passwordEye.classList.remove("eye-close-animation");
        passwordEye.classList.add("eye-open-animation");
    } else {
        passwordInput.type = "password";
        passwordEye.classList.remove("fa-eye");
        passwordEye.classList.add("fa-eye-slash");
        passwordEye.classList.remove("eye-open-animation");
        passwordEye.classList.add("eye-close-animation");
    }
}

function showNotification(title, message, type) {
    const container = document.getElementById("notification-container");
    if (notificationQueue[type].length >= 3) {
        const oldestNotification = notificationQueue[type].shift();
        oldestNotification.remove();
    }
    const notification = document.createElement("div");
    notification.classList.add("notification", type);
    notification.innerHTML = `
        <div>
            <strong>${title}</strong>
            <p>${message}</p>
        </div>
        <span class="close-btn" onclick="removeNotification(this, '${type}')">×</span>
    `;
    container.appendChild(notification);
    notificationQueue[type].push(notification);
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s ease forwards';
        setTimeout(() => {
            removeNotification(notification, type);
        }, 500);
    }, 5000);
}

function removeNotification(element, type) {
    if (element.parentElement) {
        element.parentElement.removeChild(element);
    } else {
        element.remove();
    }
    const index = notificationQueue[type].indexOf(element);
    if (index > -1) {
        notificationQueue[type].splice(index, 1);
    }
}

function fetchData(url, body) {
    return new Promise((resolve, reject) => {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => resolve(data))
        .catch(error => {
            console.error(`Error fetching from ${url}:`, error);
            reject(error);
        });
    });
}

function login(event){
    event.preventDefault();
    const username = document.querySelector('input[name="username"]').value;
    const password = document.querySelector('input[name="password"]').value;
    fetch('/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            for (const key in data.data) {
                if (data.data.hasOwnProperty(key)) {
                    localStorage.setItem(key, JSON.stringify(data.data[key]));
                }
            }
            document.cookie = `token=${localStorage.getItem('token')}; max-age=${120 * 24 * 60 * 60}; path=/;`;
            showNotification('تم تسجيل الدخول', data.message, 'success');
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        } else {
            showNotification('خطا عند تسجيل الدخول', "اسم المستخدم او كلمة السر غير صحيح", 'error');
        }
    })
    .catch(error => {
        showNotification('Error', 'An error occurred while logging in. Please try again later.', 'error');
    });
}

async function checkToken() {
    const token = localStorage.getItem('token');
    const stid = localStorage.getItem('personId');

    if (!(token && stid)) {
        return false;
    }

    document.cookie = `token=${token}; max-age=${120 * 24 * 60 * 60}; path=/;`;

    try {
        const response = await fetchData('/checktoken', { stid });
        if (response?.status) {
            return true;
        } else {
            logout();
            return false;
        }
    } catch (error) {
        console.error('Error:', error);
        return false;
    }
}