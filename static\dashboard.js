function handleButtonClick(sectionId) {
    const sections = document.querySelectorAll('section');
    const menuItems = document.querySelectorAll('.menu-item');
    sections.forEach(section => section.classList.remove('active'));
    menuItems.forEach(item => item.classList.remove('active'));
    document.getElementById(sectionId).classList.add('active');
    document.querySelector(`.menu-item[data-section="${sectionId}"]`).classList.add('active');
}

function logout() {
    for (let i = localStorage.length - 1; i >= 0; i--) { if (localStorage.key(i) !== 'wallpaper') localStorage.removeItem(localStorage.key(i)); };window.location.href = '/';
}

function reloadCSS(filename) {
    const existingLink = document.querySelector(`link[href*="${filename}"]`);

    if (existingLink) {
        const newLink = existingLink.cloneNode();
        const currentTime = new Date().getTime(); // To avoid caching

        newLink.href = `/static/${filename}`; // Add timestamp to force reload
        newLink.onload = function () {
            existingLink.remove(); // Remove old stylesheet after the new one loads
        };

        existingLink.parentNode.insertBefore(newLink, existingLink.nextSibling);
    } else {
        console.log('CSS file not found.');
    }
}

function filterSubjects() {
    const searchTerm = document.getElementById('searchBar').value.toLowerCase();
    const buttons = document.querySelectorAll('.subject-button');

    buttons.forEach(button => {
        const subjectName = button.textContent.toLowerCase();
        button.style.display = subjectName.includes(searchTerm) ? 'block' : 'none';
    });
}


function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('hidden');
}

function Spinner(action) {
    const loadingSpinner = document.getElementById('loading-spinner');
    if (action === 1) {
        loadingSpinner.classList.add('active');
    } else {
        loadingSpinner.classList.remove('active');
    }
}


function fetchData(url, body) {
    return new Promise((resolve, reject) => {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => resolve(data))
        .catch(error => {
            console.error(`Error fetching from ${url}:`, error);
            reject(error);
        });
    });
}

function fetchNotifications() {
    const notificationsBox = document.getElementById('notifications-box');
    notificationsBox.innerHTML = '';

    return fetchData('/notifications', { stid: localStorage.getItem('personId') })
        .then(result => {
            if (result.success && result.data) {
                result.data.forEach(notification => {
                    const notificationDiv = document.createElement('div');
                    notificationDiv.className = 'notification-api';
                    notificationDiv.innerHTML = `
                        <p>${notification.details}</p>
                        <small>${new Date(notification.dateInsert).toLocaleString('en-US', {
                            year: 'numeric', month: '2-digit', day: '2-digit',
                            hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true
                        })}</small>
                    `;
                    notificationsBox.appendChild(notificationDiv);
                });
            } else {
                notificationsBox.innerText = 'لا توجد إشعارات حالياً';
            }
        });
}

function fetchAbsences() {
    const tableBody = document.getElementById('absenceTableBody');
    tableBody.innerHTML = '';

    return fetchData('/getabsence', { stid: localStorage.getItem('personId') })
        .then(result => {
            if (result.success && result.data) {
                result.data.reverse().forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.notes || '-'}</td>
                        <td>${item.itemName || '-'}</td>
                        <td>${new Date(item.dateAbsence).toLocaleString('en-US', {
                            year: 'numeric', month: '2-digit', day: '2-digit',
                            hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true
                        })}</td>
                        <td>${item.name}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }
        });
}

function fetchGrades() {
    const tableBody = document.getElementById('GradesTableBody');
    tableBody.innerHTML = '';

    return fetchData('/getgrades', { stid: localStorage.getItem('personId') })
        .then(result => {
            if (result.success && result.data) {
                result.data.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.degree || '-'}</td>
                        <td>${item.itemName || '-'}</td>
                        <td>${item.monthName}</td>
                        <td>${item.yearName}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }
        });
}

function fetchExamsSchedule() {
    const tableBody = document.getElementById('ExamsScheduleTableBody');
    tableBody.innerHTML = '';

    return fetchData('/getexamsschedule', { stid: localStorage.getItem('personId') })
        .then(result => {
            if (result.success && result.data) {
                result.data.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.yearName || '-'}</td>
                        <td>${item.monthName || '-'}</td>
                        <td>${item.dayName || '-'}</td>
                        <td>${item.itemName || '-'}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }
        });
}

function fetchProfile() {
    return fetchData('/getprofile', { stid: localStorage.getItem('personId') })
        .then(result => {
            if (result.success && result.data) {
                const { userName, sexName, stageName, branchName, password } = result.data;
                document.getElementById('studentName').textContent = userName;
                document.getElementById('studentSex').textContent = sexName;
                document.getElementById('studentStage').textContent = stageName;
                document.getElementById('studentBranch').textContent = branchName;
                document.getElementById('studentpassword').textContent = password;
            } else {
                console.log('Error fetching profile:', result.message || 'Unknown error');
            }
        });
}

function fetchSubjects() {
    const container = document.getElementById('subjectsContainer');
    container.innerHTML = '';

    return fetchData('/gethomework', { stid: localStorage.getItem('personId') })
        .then(data => {
            if (data.success && data.data.length > 0) {
                data.data.forEach(({ name, lookUpId }) => {
                    const button = document.createElement('button');
                    button.className = 'subject-button';
                    button.textContent = name;
                    button.dataset.lookupid = lookUpId;
                    button.onclick = function() {fetchHomework(this)};
                    container.appendChild(button);
                });
            } else {
                console.error("Failed to fetch subjects.");
            }
        });
}


function fetchHomework(btn) {
    Spinner(1);
    const itemid = btn.getAttribute('data-lookupid');
    fetchData('/getduties', { stid: localStorage.getItem('personId'), itemid: itemid })
    .then(data => {
        if (data.success) {
            const homeworkData = data.data;
            const itemName = btn.textContent;
            const homeworkContent = document.getElementById('homeworkContent');
            homeworkContent.innerHTML = '';
            const itemHeader = `
                <div class="homework-header">
                    <h3>${itemName}</h3>
                </div>
            `;
            homeworkContent.innerHTML += itemHeader;
            homeworkData.forEach(homework => {
                const homeworkCard = `
                    <div class="homework-card">
                        <p><strong>اسم المدرس:</strong> ${homework.teacherName}</p>
                        <p><strong>تاريخ بدء الواجب:</strong> ${new Date(homework.dateStart).toLocaleString()}</p>
                        <p><strong>تاريخ انتهاء الواجب:</strong> ${new Date(homework.dateEnd).toLocaleString()}</p>
                        <p><strong>تفاصيل الواجب:</strong> ${homework.notes}</p>
                    </div>
                    <hr class="homework-separator">
                `;
                homeworkContent.innerHTML += homeworkCard;
            });
            const modal = document.getElementById('homeworkModal');
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            document.querySelector('.close').onclick = () => {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            };
            window.onclick = event => {
                if (event.target == modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            };
            Spinner(0);
        } else {
            console.error('Error fetching homework:', data);
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
    });
}


document.addEventListener('DOMContentLoaded', () => {
    Spinner(1);
    const sections = ['home', 'absences', 'homework', 'studentprofile', 'exams', 'grades'];
    const fetchFunctions = {
        home: fetchNotifications,
        absences: fetchAbsences,
        homework: fetchSubjects,
        studentprofile: fetchProfile,
        exams: fetchExamsSchedule,
        grades: fetchGrades,
    };
    if (window.innerWidth <= 768) {
        document.getElementById('sidebar').classList.add('hidden');
    }
    window.addEventListener('resize', () => {
        if (window.innerWidth >= 768) {
            document.getElementById('sidebar').classList.remove('hidden');
        }
    });
    const handleClick = (sectionId) => {
        Spinner(1);
        try {
            if (window.matchMedia("(max-width: 768px)").matches) {
                !document.getElementById('sidebar').classList.contains('hidden') && document.getElementById('sidebar').classList.add('hidden');
            }
            document.querySelectorAll('section').forEach(section => section.classList.remove('active'));
            document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
            document.getElementById(sectionId).classList.add('active');
            document.querySelector(`.menu-item[data-section="${sectionId}"]`).classList.add('active');
            fetchFunctions[sectionId]().finally(() => {
                Spinner(0);
            });
        } catch (error) {
            console.log(error);
            Spinner(0);
        }
    };
    document.querySelectorAll('.menu-item').forEach(button => {
        const sectionId = button.getAttribute('data-section');
        button.addEventListener('click', () => handleClick(sectionId));
    });
    Promise.all(sections.map(section => fetchFunctions[section]())).finally(() => {
        Spinner(0);
    });
    document.getElementById('apply-wallpaper').addEventListener('click', function() {
        document.getElementById('wallpaper-upload').click();
    });
    document.getElementById('wallpaper-upload').addEventListener('change', handleWallpaperUpload);
});

function imageToBase64(file, callback) {
    const reader = new FileReader();
    reader.onloadend = function () {
        callback(reader.result);
    };
    reader.readAsDataURL(file);
}
function applyWallpaper() {
    const wallpaper = localStorage.getItem('wallpaper');
    if (wallpaper) {
        document.body.style.backgroundImage = `url(${wallpaper})`;
        document.body.style.backgroundSize = 'cover';
        document.body.style.backgroundAttachment = 'fixed';
        document.body.style.backgroundPosition = 'center';
        document.body.style.opacity = '0.85';
    } else {
        document.body.style.backgroundImage = 'none';
        document.body.style.opacity = '1';
    }
}

function handleWallpaperUpload(event) {
    if (event.target.files.length === 0 || !event.target.files[0] || localStorage.getItem('wallpaper')) {
        localStorage.removeItem('wallpaper');
        applyWallpaper();
    }
    const file = event.target.files[0];
    if (file) {
        imageToBase64(file, function(base64Image) {
            localStorage.setItem('wallpaper', base64Image);
            applyWallpaper();
        });
    }
}
window.onload = function() {
    applyWallpaper();
};